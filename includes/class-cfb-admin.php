<?php
/**
 * CFB Calculator Admin
 * Handles admin interface and form management
 */

if (!defined('ABSPATH')) {
    exit;
}

class CFB_Admin {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('wp_ajax_cfb_delete_form', array($this, 'ajax_delete_form'));
        add_action('wp_ajax_cfb_duplicate_form', array($this, 'ajax_duplicate_form'));
        add_action('wp_ajax_cfb_toggle_form_status', array($this, 'ajax_toggle_form_status'));
    }
    
    /**
     * Render forms list page
     */
    public function render_forms_list() {
        $forms = CFB_Database::get_instance()->get_forms();
        ?>
        <div class="wrap">
            <h1 class="wp-heading-inline"><?php _e('CFB Calculator Forms', 'cfb-calculator'); ?></h1>
            <a href="<?php echo admin_url('admin.php?page=cfb-calculator-new'); ?>" class="page-title-action"><?php _e('Add New', 'cfb-calculator'); ?></a>
            
            <?php if (empty($forms)): ?>
                <div class="cfb-empty-state">
                    <div class="cfb-empty-icon">
                        <span class="dashicons dashicons-calculator"></span>
                    </div>
                    <h2><?php _e('No forms found', 'cfb-calculator'); ?></h2>
                    <p><?php _e('Create your first price calculation form to get started.', 'cfb-calculator'); ?></p>
                    <a href="<?php echo admin_url('admin.php?page=cfb-calculator-new'); ?>" class="button button-primary">
                        <?php _e('Create Your First Form', 'cfb-calculator'); ?>
                    </a>
                </div>
            <?php else: ?>
                <div class="cfb-forms-grid">
                    <?php foreach ($forms as $form): ?>
                        <?php $this->render_form_card($form); ?>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
        
        <style>
        .cfb-empty-state {
            text-align: center;
            padding: 60px 20px;
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            margin-top: 20px;
        }
        .cfb-empty-icon .dashicons {
            font-size: 64px;
            color: #c3c4c7;
            margin-bottom: 20px;
        }
        .cfb-forms-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .cfb-form-card {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
            transition: box-shadow 0.2s;
        }
        .cfb-form-card:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .cfb-form-title {
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 10px 0;
        }
        .cfb-form-description {
            color: #646970;
            margin-bottom: 15px;
            font-size: 14px;
        }
        .cfb-form-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 12px;
            color: #646970;
        }
        .cfb-form-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .cfb-form-actions .button {
            font-size: 12px;
            padding: 4px 8px;
            height: auto;
        }
        .cfb-status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        .cfb-status-active {
            background: #d1e7dd;
            color: #0f5132;
        }
        .cfb-status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        .cfb-shortcode {
            background: #f6f7f7;
            padding: 8px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            margin-top: 10px;
            cursor: pointer;
            user-select: all;
        }
        </style>
        <?php
    }
    
    /**
     * Render individual form card
     */
    private function render_form_card($form) {
        $form_data = json_decode($form->form_data, true);
        $field_count = isset($form_data['fields']) ? count($form_data['fields']) : 0;
        $submissions_count = CFB_Database::get_instance()->get_submission_count($form->id);
        ?>
        <div class="cfb-form-card">
            <h3 class="cfb-form-title"><?php echo esc_html($form->name); ?></h3>
            
            <?php if (!empty($form->description)): ?>
                <div class="cfb-form-description">
                    <?php echo esc_html(wp_trim_words($form->description, 20)); ?>
                </div>
            <?php endif; ?>
            
            <div class="cfb-form-meta">
                <div>
                    <span class="cfb-status-badge cfb-status-<?php echo esc_attr($form->status); ?>">
                        <?php echo $form->status === 'active' ? __('Active', 'cfb-calculator') : __('Inactive', 'cfb-calculator'); ?>
                    </span>
                </div>
                <div>
                    <?php printf(__('%d fields', 'cfb-calculator'), $field_count); ?> • 
                    <?php printf(__('%d submissions', 'cfb-calculator'), $submissions_count); ?>
                </div>
            </div>
            
            <div class="cfb-form-actions">
                <a href="<?php echo admin_url('admin.php?page=cfb-calculator-new&form_id=' . $form->id); ?>" class="button button-primary">
                    <?php _e('Edit', 'cfb-calculator'); ?>
                </a>
                <button type="button" class="button cfb-duplicate-form" data-form-id="<?php echo esc_attr($form->id); ?>">
                    <?php _e('Duplicate', 'cfb-calculator'); ?>
                </button>
                <button type="button" class="button cfb-toggle-status" data-form-id="<?php echo esc_attr($form->id); ?>" data-status="<?php echo esc_attr($form->status); ?>">
                    <?php echo $form->status === 'active' ? __('Deactivate', 'cfb-calculator') : __('Activate', 'cfb-calculator'); ?>
                </button>
                <button type="button" class="button cfb-delete-form" data-form-id="<?php echo esc_attr($form->id); ?>">
                    <?php _e('Delete', 'cfb-calculator'); ?>
                </button>
            </div>
            
            <div class="cfb-shortcode" title="<?php _e('Click to copy shortcode', 'cfb-calculator'); ?>">
                [cfb_calculator id="<?php echo esc_attr($form->id); ?>"]
            </div>
            
            <div class="cfb-form-meta" style="margin-top: 10px; margin-bottom: 0;">
                <small>
                    <?php printf(__('Created: %s', 'cfb-calculator'), date_i18n(get_option('date_format'), strtotime($form->created_at))); ?>
                </small>
                <small>
                    <?php printf(__('Updated: %s', 'cfb-calculator'), date_i18n(get_option('date_format'), strtotime($form->updated_at))); ?>
                </small>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render form builder page
     */
    public function render_form_builder() {
        $form_id = isset($_GET['form_id']) ? intval($_GET['form_id']) : 0;
        CFB_Form_Builder::get_instance()->render_form_builder($form_id);
    }
    
    /**
     * AJAX handler for deleting forms
     */
    public function ajax_delete_form() {
        check_ajax_referer('cfb_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Unauthorized access', 'cfb-calculator'));
        }
        
        $form_id = intval($_POST['form_id']);
        
        if (!$form_id) {
            wp_send_json_error(__('Invalid form ID', 'cfb-calculator'));
        }
        
        $result = CFB_Database::get_instance()->delete_form($form_id);
        
        if ($result) {
            wp_send_json_success(__('Form deleted successfully', 'cfb-calculator'));
        } else {
            wp_send_json_error(__('Failed to delete form', 'cfb-calculator'));
        }
    }
    
    /**
     * AJAX handler for duplicating forms
     */
    public function ajax_duplicate_form() {
        check_ajax_referer('cfb_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Unauthorized access', 'cfb-calculator'));
        }
        
        $form_id = intval($_POST['form_id']);
        
        if (!$form_id) {
            wp_send_json_error(__('Invalid form ID', 'cfb-calculator'));
        }
        
        $original_form = CFB_Database::get_instance()->get_form($form_id);
        
        if (!$original_form) {
            wp_send_json_error(__('Form not found', 'cfb-calculator'));
        }
        
        // Create duplicate
        $duplicate_data = array(
            'name' => $original_form->name . ' (Copy)',
            'description' => $original_form->description,
            'form_data' => json_decode($original_form->form_data, true),
            'settings' => json_decode($original_form->settings, true),
            'status' => 'inactive'
        );
        
        $new_form_id = CFB_Database::get_instance()->save_form($duplicate_data);
        
        if ($new_form_id) {
            wp_send_json_success(array(
                'message' => __('Form duplicated successfully', 'cfb-calculator'),
                'new_form_id' => $new_form_id
            ));
        } else {
            wp_send_json_error(__('Failed to duplicate form', 'cfb-calculator'));
        }
    }
    
    /**
     * AJAX handler for toggling form status
     */
    public function ajax_toggle_form_status() {
        check_ajax_referer('cfb_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Unauthorized access', 'cfb-calculator'));
        }
        
        $form_id = intval($_POST['form_id']);
        $current_status = sanitize_text_field($_POST['current_status']);
        
        if (!$form_id) {
            wp_send_json_error(__('Invalid form ID', 'cfb-calculator'));
        }
        
        $new_status = $current_status === 'active' ? 'inactive' : 'active';
        
        global $wpdb;
        $table_forms = $wpdb->prefix . 'cfb_forms';
        
        $result = $wpdb->update(
            $table_forms,
            array('status' => $new_status),
            array('id' => $form_id),
            array('%s'),
            array('%d')
        );
        
        if ($result !== false) {
            wp_send_json_success(array(
                'message' => sprintf(__('Form %s successfully', 'cfb-calculator'), $new_status === 'active' ? __('activated', 'cfb-calculator') : __('deactivated', 'cfb-calculator')),
                'new_status' => $new_status
            ));
        } else {
            wp_send_json_error(__('Failed to update form status', 'cfb-calculator'));
        }
    }
    
    /**
     * Add admin notices
     */
    public function add_admin_notices() {
        if (isset($_GET['cfb_message'])) {
            $message_type = $_GET['cfb_message'];
            $messages = array(
                'form_saved' => __('Form saved successfully!', 'cfb-calculator'),
                'form_deleted' => __('Form deleted successfully!', 'cfb-calculator'),
                'form_duplicated' => __('Form duplicated successfully!', 'cfb-calculator')
            );
            
            if (isset($messages[$message_type])) {
                ?>
                <div class="notice notice-success is-dismissible">
                    <p><?php echo esc_html($messages[$message_type]); ?></p>
                </div>
                <?php
            }
        }
    }
}
