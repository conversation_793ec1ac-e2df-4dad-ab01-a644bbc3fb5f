<?php
/**
 * CFB Calculator Formula Engine
 * Handles complex formula calculations with support for mathematical functions
 */

if (!defined('ABSPATH')) {
    exit;
}

class CFB_Formula_Engine {
    
    private static $instance = null;
    private $variables = array();
    private $functions = array();
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_functions();
    }
    
    /**
     * Initialize supported functions
     */
    private function init_functions() {
        $this->functions = array(
            'ceil' => 'ceil',
            'floor' => 'floor',
            'min' => 'min',
            'max' => 'max',
            'pow' => 'pow',
            'sqrt' => 'sqrt',
            'abs' => 'abs',
            'round' => 'round',
            'if' => array($this, 'if_function'),
            'ifelse' => array($this, 'ifelse_function')
        );
    }
    
    /**
     * AJAX handler for price calculation
     */
    public function calculate_price() {
        $form_id = intval($_POST['form_id']);
        $form_data = $_POST['form_data'];
        
        if (!$form_id || !$form_data) {
            wp_send_json_error(__('Invalid form data', 'cfb-calculator'));
        }
        
        // Get form configuration
        $form = CFB_Database::get_instance()->get_form($form_id);
        if (!$form) {
            wp_send_json_error(__('Form not found', 'cfb-calculator'));
        }
        
        $form_config = json_decode($form->form_data, true);
        $settings = json_decode($form->settings, true);
        
        try {
            // Process form data and calculate
            $result = $this->process_calculation($form_data, $form_config, $settings);
            
            // Save submission if enabled
            if (isset($settings['save_submissions']) && $settings['save_submissions']) {
                CFB_Database::get_instance()->save_submission($form_id, $form_data, $result['total']);
            }
            
            wp_send_json_success($result);
            
        } catch (Exception $e) {
            wp_send_json_error($e->getMessage());
        }
    }
    
    /**
     * Process calculation based on form data and configuration
     */
    public function process_calculation($form_data, $form_config, $settings) {
        $this->variables = array();
        $subtotals = array();
        $total = 0;
        
        // Process each field and extract values
        foreach ($form_config['fields'] as $field) {
            $field_name = $field['name'];
            $field_value = isset($form_data[$field_name]) ? $form_data[$field_name] : '';
            
            // Check conditional logic
            if (!$this->check_conditional_logic($field, $form_data)) {
                continue;
            }
            
            // Process field value based on type
            $processed_value = $this->process_field_value($field, $field_value);
            $this->variables[$field_name] = $processed_value;
        }
        
        // Calculate subtotals
        if (isset($form_config['subtotals']) && is_array($form_config['subtotals'])) {
            foreach ($form_config['subtotals'] as $index => $subtotal_config) {
                $subtotal_value = $this->evaluate_formula($subtotal_config['formula']);
                $subtotals[] = array(
                    'label' => $subtotal_config['label'],
                    'value' => $subtotal_value,
                    'formatted' => $this->format_currency($subtotal_value, $settings)
                );
                
                // Add subtotal to variables for use in total calculation
                $this->variables['subtotal_' . ($index + 1)] = $subtotal_value;
            }
        }
        
        // Calculate total
        if (isset($form_config['total_formula']) && !empty($form_config['total_formula'])) {
            $total = $this->evaluate_formula($form_config['total_formula']);
        } else {
            // Default: sum all subtotals
            $total = array_sum(array_column($subtotals, 'value'));
        }
        
        return array(
            'subtotals' => $subtotals,
            'total' => $total,
            'formatted_total' => $this->format_currency($total, $settings),
            'variables' => $this->variables
        );
    }
    
    /**
     * Process field value based on field type
     */
    private function process_field_value($field, $value) {
        switch ($field['type']) {
            case 'number':
            case 'slider':
                return floatval($value);
                
            case 'dropdown':
            case 'radio':
                // Find the selected option and return its value
                if (isset($field['options']) && is_array($field['options'])) {
                    foreach ($field['options'] as $option) {
                        if ($option['value'] === $value) {
                            return isset($option['price']) ? floatval($option['price']) : 0;
                        }
                    }
                }
                return 0;
                
            case 'checkbox':
                $total = 0;
                if (is_array($value)) {
                    foreach ($value as $selected_value) {
                        if (isset($field['options']) && is_array($field['options'])) {
                            foreach ($field['options'] as $option) {
                                if ($option['value'] === $selected_value) {
                                    $total += isset($option['price']) ? floatval($option['price']) : 0;
                                }
                            }
                        }
                    }
                }
                return $total;
                
            case 'text':
                // For text fields, check if there's a calculation rule
                if (isset($field['calculation_rule'])) {
                    return $this->apply_text_calculation_rule($value, $field['calculation_rule']);
                }
                return 0;
                
            default:
                return 0;
        }
    }
    
    /**
     * Apply calculation rule for text fields
     */
    private function apply_text_calculation_rule($value, $rule) {
        switch ($rule['type']) {
            case 'per_character':
                return strlen($value) * floatval($rule['rate']);
                
            case 'per_word':
                return str_word_count($value) * floatval($rule['rate']);
                
            case 'fixed_rate':
                return !empty($value) ? floatval($rule['rate']) : 0;
                
            default:
                return 0;
        }
    }
    
    /**
     * Check conditional logic for a field
     */
    private function check_conditional_logic($field, $form_data) {
        if (!isset($field['conditional_logic']) || !$field['conditional_logic']['enabled']) {
            return true;
        }
        
        $logic = $field['conditional_logic'];
        $conditions_met = 0;
        
        foreach ($logic['conditions'] as $condition) {
            $field_value = isset($form_data[$condition['field']]) ? $form_data[$condition['field']] : '';
            $condition_met = false;
            
            switch ($condition['operator']) {
                case 'equals':
                    $condition_met = ($field_value == $condition['value']);
                    break;
                case 'not_equals':
                    $condition_met = ($field_value != $condition['value']);
                    break;
                case 'greater_than':
                    $condition_met = (floatval($field_value) > floatval($condition['value']));
                    break;
                case 'less_than':
                    $condition_met = (floatval($field_value) < floatval($condition['value']));
                    break;
                case 'contains':
                    $condition_met = (strpos($field_value, $condition['value']) !== false);
                    break;
                case 'not_empty':
                    $condition_met = !empty($field_value);
                    break;
                case 'empty':
                    $condition_met = empty($field_value);
                    break;
            }
            
            if ($condition_met) {
                $conditions_met++;
            }
        }
        
        // Check if conditions are met based on logic type
        if ($logic['logic_type'] === 'all') {
            return $conditions_met === count($logic['conditions']);
        } else {
            return $conditions_met > 0;
        }
    }
    
    /**
     * Evaluate mathematical formula
     */
    public function evaluate_formula($formula) {
        if (empty($formula)) {
            return 0;
        }
        
        // Replace variables with their values
        $formula = $this->replace_variables($formula);
        
        // Replace function calls
        $formula = $this->replace_functions($formula);
        
        // Evaluate the mathematical expression safely
        return $this->safe_eval($formula);
    }
    
    /**
     * Replace variables in formula with their values
     */
    private function replace_variables($formula) {
        foreach ($this->variables as $var_name => $var_value) {
            $formula = str_replace('{' . $var_name . '}', $var_value, $formula);
        }
        return $formula;
    }
    
    /**
     * Replace function calls in formula
     */
    private function replace_functions($formula) {
        // Handle function calls like ceil(5.7), min(a,b), if(condition,true_value,false_value)
        $pattern = '/(\w+)\s*\(([^)]+)\)/';
        
        while (preg_match($pattern, $formula, $matches)) {
            $function_name = strtolower($matches[1]);
            $params = $matches[2];
            
            if (isset($this->functions[$function_name])) {
                $result = $this->call_function($function_name, $params);
                $formula = str_replace($matches[0], $result, $formula);
            } else {
                // Unknown function, replace with 0
                $formula = str_replace($matches[0], '0', $formula);
            }
        }
        
        return $formula;
    }
    
    /**
     * Call a function with parameters
     */
    private function call_function($function_name, $params_string) {
        $params = array_map('trim', explode(',', $params_string));
        
        // Evaluate each parameter
        foreach ($params as &$param) {
            $param = $this->safe_eval($param);
        }
        
        $function = $this->functions[$function_name];
        
        if (is_callable($function)) {
            return call_user_func_array($function, $params);
        }
        
        return 0;
    }
    
    /**
     * IF function implementation
     */
    public function if_function($condition, $true_value, $false_value = 0) {
        return $condition ? $true_value : $false_value;
    }
    
    /**
     * IFELSE function implementation (alias for IF)
     */
    public function ifelse_function($condition, $true_value, $false_value = 0) {
        return $this->if_function($condition, $true_value, $false_value);
    }
    
    /**
     * Safely evaluate mathematical expression
     */
    private function safe_eval($expression) {
        // Remove any non-mathematical characters for security
        $expression = preg_replace('/[^0-9+\-*\/\(\)\.\s]/', '', $expression);
        
        if (empty($expression)) {
            return 0;
        }
        
        // Use eval carefully with sanitized input
        try {
            $result = eval("return $expression;");
            return is_numeric($result) ? floatval($result) : 0;
        } catch (Exception $e) {
            return 0;
        }
    }
    
    /**
     * Format currency value
     */
    private function format_currency($value, $settings) {
        $decimal_places = isset($settings['decimal_places']) ? intval($settings['decimal_places']) : 2;
        $decimal_separator = isset($settings['decimal_separator']) ? $settings['decimal_separator'] : '.';
        $thousand_separator = isset($settings['thousand_separator']) ? $settings['thousand_separator'] : ',';
        $currency_symbol = isset($settings['currency_symbol']) ? $settings['currency_symbol'] : '$';
        $currency_position = isset($settings['currency_position']) ? $settings['currency_position'] : 'left';
        
        $formatted_number = number_format($value, $decimal_places, $decimal_separator, $thousand_separator);
        
        if ($currency_position === 'right') {
            return $formatted_number . ' ' . $currency_symbol;
        } else {
            return $currency_symbol . ' ' . $formatted_number;
        }
    }
}
