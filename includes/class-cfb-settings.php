<?php
/**
 * CFB Calculator Settings
 * Handles plugin settings and configuration
 */

if (!defined('ABSPATH')) {
    exit;
}

class CFB_Settings {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('admin_init', array($this, 'register_settings'));
        add_action('wp_ajax_cfb_save_settings', array($this, 'ajax_save_settings'));
    }
    
    /**
     * Register plugin settings
     */
    public function register_settings() {
        // Currency Settings
        register_setting('cfb_calculator_settings', 'cfb_currency_symbol');
        register_setting('cfb_calculator_settings', 'cfb_currency_position');
        register_setting('cfb_calculator_settings', 'cfb_decimal_places');
        register_setting('cfb_calculator_settings', 'cfb_thousand_separator');
        register_setting('cfb_calculator_settings', 'cfb_decimal_separator');
        
        // Display Settings
        register_setting('cfb_calculator_settings', 'cfb_default_theme');
        register_setting('cfb_calculator_settings', 'cfb_enable_animations');
        register_setting('cfb_calculator_settings', 'cfb_auto_calculate');
        
        // Language Settings
        register_setting('cfb_calculator_settings', 'cfb_rtl_support');
        register_setting('cfb_calculator_settings', 'cfb_default_language');
        
        // Advanced Settings
        register_setting('cfb_calculator_settings', 'cfb_enable_caching');
        register_setting('cfb_calculator_settings', 'cfb_debug_mode');
        register_setting('cfb_calculator_settings', 'cfb_custom_css');
    }
    
    /**
     * Render settings page
     */
    public function render_settings_page() {
        ?>
        <div class="wrap cfb-settings-page">
            <h1><?php _e('CFB Calculator Settings', 'cfb-calculator'); ?></h1>
            
            <div class="cfb-settings-container">
                <div class="cfb-settings-nav">
                    <ul class="cfb-tabs">
                        <li class="cfb-tab active" data-tab="currency">
                            <span class="dashicons dashicons-money-alt"></span>
                            <?php _e('Currency', 'cfb-calculator'); ?>
                        </li>
                        <li class="cfb-tab" data-tab="display">
                            <span class="dashicons dashicons-admin-appearance"></span>
                            <?php _e('Display', 'cfb-calculator'); ?>
                        </li>
                        <li class="cfb-tab" data-tab="language">
                            <span class="dashicons dashicons-translation"></span>
                            <?php _e('Language', 'cfb-calculator'); ?>
                        </li>
                        <li class="cfb-tab" data-tab="advanced">
                            <span class="dashicons dashicons-admin-tools"></span>
                            <?php _e('Advanced', 'cfb-calculator'); ?>
                        </li>
                    </ul>
                </div>
                
                <div class="cfb-settings-content">
                    <form id="cfb-settings-form">
                        <?php wp_nonce_field('cfb_settings_nonce', 'cfb_settings_nonce'); ?>
                        
                        <!-- Currency Settings -->
                        <div class="cfb-tab-content active" id="currency-tab">
                            <h2><?php _e('Currency Settings', 'cfb-calculator'); ?></h2>
                            
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_currency_symbol"><?php _e('Currency Symbol', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <input type="text" 
                                               id="cfb_currency_symbol" 
                                               name="cfb_currency_symbol" 
                                               value="<?php echo esc_attr(get_option('cfb_currency_symbol', '$')); ?>" 
                                               class="regular-text">
                                        <p class="description"><?php _e('The symbol to display for currency (e.g., $, €, £, ﷼)', 'cfb-calculator'); ?></p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_currency_position"><?php _e('Currency Position', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <select id="cfb_currency_position" name="cfb_currency_position">
                                            <option value="left" <?php selected(get_option('cfb_currency_position', 'left'), 'left'); ?>>
                                                <?php _e('Left ($100)', 'cfb-calculator'); ?>
                                            </option>
                                            <option value="right" <?php selected(get_option('cfb_currency_position', 'left'), 'right'); ?>>
                                                <?php _e('Right (100$)', 'cfb-calculator'); ?>
                                            </option>
                                        </select>
                                        <p class="description"><?php _e('Position of currency symbol relative to the amount', 'cfb-calculator'); ?></p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_decimal_places"><?php _e('Decimal Places', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <input type="number" 
                                               id="cfb_decimal_places" 
                                               name="cfb_decimal_places" 
                                               value="<?php echo esc_attr(get_option('cfb_decimal_places', 2)); ?>" 
                                               min="0" 
                                               max="4" 
                                               class="small-text">
                                        <p class="description"><?php _e('Number of decimal places to display', 'cfb-calculator'); ?></p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_thousand_separator"><?php _e('Thousand Separator', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <input type="text" 
                                               id="cfb_thousand_separator" 
                                               name="cfb_thousand_separator" 
                                               value="<?php echo esc_attr(get_option('cfb_thousand_separator', ',')); ?>" 
                                               class="small-text">
                                        <p class="description"><?php _e('Character used to separate thousands (e.g., comma, space)', 'cfb-calculator'); ?></p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_decimal_separator"><?php _e('Decimal Separator', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <input type="text" 
                                               id="cfb_decimal_separator" 
                                               name="cfb_decimal_separator" 
                                               value="<?php echo esc_attr(get_option('cfb_decimal_separator', '.')); ?>" 
                                               class="small-text">
                                        <p class="description"><?php _e('Character used to separate decimal places', 'cfb-calculator'); ?></p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        
                        <!-- Display Settings -->
                        <div class="cfb-tab-content" id="display-tab">
                            <h2><?php _e('Display Settings', 'cfb-calculator'); ?></h2>
                            
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_default_theme"><?php _e('Default Theme', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <select id="cfb_default_theme" name="cfb_default_theme">
                                            <option value="modern" <?php selected(get_option('cfb_default_theme', 'modern'), 'modern'); ?>>
                                                <?php _e('Modern', 'cfb-calculator'); ?>
                                            </option>
                                            <option value="classic" <?php selected(get_option('cfb_default_theme', 'modern'), 'classic'); ?>>
                                                <?php _e('Classic', 'cfb-calculator'); ?>
                                            </option>
                                            <option value="minimal" <?php selected(get_option('cfb_default_theme', 'modern'), 'minimal'); ?>>
                                                <?php _e('Minimal', 'cfb-calculator'); ?>
                                            </option>
                                        </select>
                                        <p class="description"><?php _e('Default theme for calculator forms', 'cfb-calculator'); ?></p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_enable_animations"><?php _e('Enable Animations', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <label>
                                            <input type="checkbox" 
                                                   id="cfb_enable_animations" 
                                                   name="cfb_enable_animations" 
                                                   value="1" 
                                                   <?php checked(get_option('cfb_enable_animations', 1), 1); ?>>
                                            <?php _e('Enable smooth animations and transitions', 'cfb-calculator'); ?>
                                        </label>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_auto_calculate"><?php _e('Auto Calculate', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <label>
                                            <input type="checkbox" 
                                                   id="cfb_auto_calculate" 
                                                   name="cfb_auto_calculate" 
                                                   value="1" 
                                                   <?php checked(get_option('cfb_auto_calculate', 1), 1); ?>>
                                            <?php _e('Calculate automatically when form values change', 'cfb-calculator'); ?>
                                        </label>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        
                        <!-- Language Settings -->
                        <div class="cfb-tab-content" id="language-tab">
                            <h2><?php _e('Language & RTL Settings', 'cfb-calculator'); ?></h2>
                            
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_rtl_support"><?php _e('RTL Support', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <label>
                                            <input type="checkbox" 
                                                   id="cfb_rtl_support" 
                                                   name="cfb_rtl_support" 
                                                   value="1" 
                                                   <?php checked(get_option('cfb_rtl_support', 1), 1); ?>>
                                            <?php _e('Enable Right-to-Left (RTL) language support', 'cfb-calculator'); ?>
                                        </label>
                                        <p class="description"><?php _e('Automatically adjusts layout for RTL languages like Arabic, Hebrew, Farsi', 'cfb-calculator'); ?></p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_default_language"><?php _e('Default Language', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <select id="cfb_default_language" name="cfb_default_language">
                                            <option value="en" <?php selected(get_option('cfb_default_language', 'en'), 'en'); ?>>
                                                <?php _e('English', 'cfb-calculator'); ?>
                                            </option>
                                            <option value="fa" <?php selected(get_option('cfb_default_language', 'en'), 'fa'); ?>>
                                                <?php _e('Farsi (Persian)', 'cfb-calculator'); ?>
                                            </option>
                                            <option value="ar" <?php selected(get_option('cfb_default_language', 'en'), 'ar'); ?>>
                                                <?php _e('Arabic', 'cfb-calculator'); ?>
                                            </option>
                                        </select>
                                        <p class="description"><?php _e('Default language for new forms', 'cfb-calculator'); ?></p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        
                        <!-- Advanced Settings -->
                        <div class="cfb-tab-content" id="advanced-tab">
                            <h2><?php _e('Advanced Settings', 'cfb-calculator'); ?></h2>
                            
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_enable_caching"><?php _e('Enable Caching', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <label>
                                            <input type="checkbox" 
                                                   id="cfb_enable_caching" 
                                                   name="cfb_enable_caching" 
                                                   value="1" 
                                                   <?php checked(get_option('cfb_enable_caching', 1), 1); ?>>
                                            <?php _e('Cache calculation results for better performance', 'cfb-calculator'); ?>
                                        </label>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_debug_mode"><?php _e('Debug Mode', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <label>
                                            <input type="checkbox" 
                                                   id="cfb_debug_mode" 
                                                   name="cfb_debug_mode" 
                                                   value="1" 
                                                   <?php checked(get_option('cfb_debug_mode', 0), 1); ?>>
                                            <?php _e('Enable debug mode for troubleshooting', 'cfb-calculator'); ?>
                                        </label>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="cfb_custom_css"><?php _e('Custom CSS', 'cfb-calculator'); ?></label>
                                    </th>
                                    <td>
                                        <textarea id="cfb_custom_css" 
                                                  name="cfb_custom_css" 
                                                  rows="10" 
                                                  cols="50" 
                                                  class="large-text code"><?php echo esc_textarea(get_option('cfb_custom_css', '')); ?></textarea>
                                        <p class="description"><?php _e('Add custom CSS to style your calculator forms', 'cfb-calculator'); ?></p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        
                        <p class="submit">
                            <button type="submit" class="button-primary"><?php _e('Save Settings', 'cfb-calculator'); ?></button>
                            <button type="button" class="button" id="cfb-reset-settings"><?php _e('Reset to Defaults', 'cfb-calculator'); ?></button>
                        </p>
                    </form>
                </div>
            </div>
        </div>
        
        <style>
        .cfb-settings-container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }
        .cfb-settings-nav {
            flex: 0 0 200px;
        }
        .cfb-tabs {
            list-style: none;
            margin: 0;
            padding: 0;
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
        }
        .cfb-tab {
            border-bottom: 1px solid #ccd0d4;
            cursor: pointer;
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: background-color 0.2s;
        }
        .cfb-tab:last-child {
            border-bottom: none;
        }
        .cfb-tab:hover {
            background: #f6f7f7;
        }
        .cfb-tab.active {
            background: #0073aa;
            color: #fff;
        }
        .cfb-settings-content {
            flex: 1;
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            padding: 20px;
        }
        .cfb-tab-content {
            display: none;
        }
        .cfb-tab-content.active {
            display: block;
        }
        </style>
        <?php
    }
    
    /**
     * AJAX handler for saving settings
     */
    public function ajax_save_settings() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cfb_settings_nonce')) {
            wp_send_json_error(__('Security check failed', 'cfb-calculator'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Unauthorized access', 'cfb-calculator'));
        }

        $settings = isset($_POST['settings']) ? $_POST['settings'] : array();

        foreach ($settings as $key => $value) {
            if (in_array($key, array('cfb_custom_css'))) {
                // Don't sanitize CSS
                update_option($key, wp_kses_post($value));
            } else {
                update_option($key, sanitize_text_field($value));
            }
        }

        wp_send_json_success(__('Settings saved successfully', 'cfb-calculator'));
    }
    
    /**
     * Get default settings
     */
    public function get_default_settings() {
        return array(
            'cfb_currency_symbol' => '$',
            'cfb_currency_position' => 'left',
            'cfb_decimal_places' => 2,
            'cfb_thousand_separator' => ',',
            'cfb_decimal_separator' => '.',
            'cfb_default_theme' => 'modern',
            'cfb_enable_animations' => 1,
            'cfb_auto_calculate' => 1,
            'cfb_rtl_support' => 1,
            'cfb_default_language' => 'en',
            'cfb_enable_caching' => 1,
            'cfb_debug_mode' => 0,
            'cfb_custom_css' => ''
        );
    }
}
