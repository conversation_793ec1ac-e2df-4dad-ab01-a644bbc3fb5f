<?php
/**
 * CFB Calculator Form Builder
 * Handles form creation and management in admin
 */

if (!defined('ABSPATH')) {
    exit;
}

class CFB_Form_Builder {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        // Constructor
    }
    
    /**
     * Get available field types
     */
    public function get_field_types() {
        return array(
            'text' => array(
                'label' => __('Text Field', 'cfb-calculator'),
                'icon' => 'dashicons-edit',
                'supports' => array('conditional_logic', 'calculation_rule')
            ),
            'number' => array(
                'label' => __('Number Field', 'cfb-calculator'),
                'icon' => 'dashicons-calculator',
                'supports' => array('conditional_logic', 'min_max')
            ),
            'slider' => array(
                'label' => __('Slider', 'cfb-calculator'),
                'icon' => 'dashicons-leftright',
                'supports' => array('conditional_logic', 'min_max', 'step')
            ),
            'dropdown' => array(
                'label' => __('Dropdown', 'cfb-calculator'),
                'icon' => 'dashicons-arrow-down-alt2',
                'supports' => array('conditional_logic', 'options')
            ),
            'radio' => array(
                'label' => __('Radio Buttons', 'cfb-calculator'),
                'icon' => 'dashicons-marker',
                'supports' => array('conditional_logic', 'options')
            ),
            'checkbox' => array(
                'label' => __('Checkboxes', 'cfb-calculator'),
                'icon' => 'dashicons-yes',
                'supports' => array('conditional_logic', 'options')
            ),
            'calculation' => array(
                'label' => __('Calculation Field', 'cfb-calculator'),
                'icon' => 'dashicons-chart-line',
                'supports' => array('conditional_logic', 'formula')
            ),
            'total' => array(
                'label' => __('Total Field', 'cfb-calculator'),
                'icon' => 'dashicons-money-alt',
                'supports' => array('conditional_logic', 'formula')
            )
        );
    }
    
    /**
     * Get default field configuration
     */
    public function get_default_field_config($type) {
        $defaults = array(
            'text' => array(
                'type' => 'text',
                'label' => __('Text Field', 'cfb-calculator'),
                'name' => 'text_field_' . time(),
                'placeholder' => '',
                'required' => false,
                'calculation_rule' => array(
                    'type' => 'per_character',
                    'rate' => 0
                ),
                'conditional_logic' => array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'conditions' => array()
                )
            ),
            'number' => array(
                'type' => 'number',
                'label' => __('Number Field', 'cfb-calculator'),
                'name' => 'number_field_' . time(),
                'min' => 0,
                'max' => 100,
                'step' => 1,
                'default_value' => 0,
                'required' => false,
                'conditional_logic' => array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'conditions' => array()
                )
            ),
            'slider' => array(
                'type' => 'slider',
                'label' => __('Slider', 'cfb-calculator'),
                'name' => 'slider_field_' . time(),
                'min' => 0,
                'max' => 100,
                'step' => 1,
                'default_value' => 50,
                'show_value' => true,
                'conditional_logic' => array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'conditions' => array()
                )
            ),
            'dropdown' => array(
                'type' => 'dropdown',
                'label' => __('Dropdown', 'cfb-calculator'),
                'name' => 'dropdown_field_' . time(),
                'placeholder' => __('Select an option', 'cfb-calculator'),
                'required' => false,
                'options' => array(
                    array('label' => __('Option 1', 'cfb-calculator'), 'value' => 'option1', 'price' => 0),
                    array('label' => __('Option 2', 'cfb-calculator'), 'value' => 'option2', 'price' => 0)
                ),
                'conditional_logic' => array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'conditions' => array()
                )
            ),
            'radio' => array(
                'type' => 'radio',
                'label' => __('Radio Buttons', 'cfb-calculator'),
                'name' => 'radio_field_' . time(),
                'required' => false,
                'options' => array(
                    array('label' => __('Option 1', 'cfb-calculator'), 'value' => 'option1', 'price' => 0),
                    array('label' => __('Option 2', 'cfb-calculator'), 'value' => 'option2', 'price' => 0)
                ),
                'conditional_logic' => array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'conditions' => array()
                )
            ),
            'checkbox' => array(
                'type' => 'checkbox',
                'label' => __('Checkboxes', 'cfb-calculator'),
                'name' => 'checkbox_field_' . time(),
                'options' => array(
                    array('label' => __('Option 1', 'cfb-calculator'), 'value' => 'option1', 'price' => 0),
                    array('label' => __('Option 2', 'cfb-calculator'), 'value' => 'option2', 'price' => 0)
                ),
                'conditional_logic' => array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'conditions' => array()
                )
            ),
            'calculation' => array(
                'type' => 'calculation',
                'label' => __('Calculation', 'cfb-calculator'),
                'name' => 'calculation_field_' . time(),
                'formula' => '',
                'display_type' => 'number', // number, currency, percentage
                'conditional_logic' => array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'conditions' => array()
                )
            ),
            'total' => array(
                'type' => 'total',
                'label' => __('Total', 'cfb-calculator'),
                'name' => 'total_field_' . time(),
                'formula' => '',
                'display_type' => 'currency',
                'show_breakdown' => true,
                'conditional_logic' => array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'conditions' => array()
                )
            )
        );
        
        return isset($defaults[$type]) ? $defaults[$type] : array();
    }
    
    /**
     * Save form via AJAX
     */
    public function save_form() {
        $form_data = $_POST['form_data'];
        $form_id = isset($_POST['form_id']) ? intval($_POST['form_id']) : 0;
        
        // Validate required fields
        if (empty($form_data['name'])) {
            wp_send_json_error(__('Form name is required', 'cfb-calculator'));
        }
        
        // Prepare data for saving
        $save_data = array(
            'name' => sanitize_text_field($form_data['name']),
            'description' => sanitize_textarea_field($form_data['description']),
            'form_data' => $form_data,
            'settings' => isset($form_data['settings']) ? $form_data['settings'] : array(),
            'status' => 'active'
        );
        
        if ($form_id) {
            $save_data['id'] = $form_id;
        }
        
        $saved_id = CFB_Database::get_instance()->save_form($save_data);
        
        if ($saved_id) {
            wp_send_json_success(array(
                'form_id' => $saved_id,
                'message' => __('Form saved successfully', 'cfb-calculator')
            ));
        } else {
            wp_send_json_error(__('Failed to save form', 'cfb-calculator'));
        }
    }
    
    /**
     * Render form builder interface
     */
    public function render_form_builder($form_id = 0) {
        $form = null;
        $form_data = array();

        if ($form_id) {
            $form = CFB_Database::get_instance()->get_form($form_id);
            if ($form) {
                $form_data = json_decode($form->form_data, true);
                if (!$form_data) {
                    $form_data = array();
                }
            }
        }

        $field_types = $this->get_field_types();
        ?>
        <div class="wrap cfb-form-builder">
            <h1><?php echo $form_id ? __('Edit Form', 'cfb-calculator') : __('Create New Form', 'cfb-calculator'); ?></h1>
            
            <div class="cfb-builder-container">
                <!-- Form Settings Panel -->
                <div class="cfb-settings-panel">
                    <h3><?php _e('Form Settings', 'cfb-calculator'); ?></h3>
                    
                    <div class="cfb-form-group">
                        <label for="form-name"><?php _e('Form Name', 'cfb-calculator'); ?></label>
                        <input type="text" id="form-name" value="<?php echo $form ? esc_attr($form->name) : ''; ?>" placeholder="<?php _e('Enter form name', 'cfb-calculator'); ?>">
                    </div>
                    
                    <div class="cfb-form-group">
                        <label for="form-description"><?php _e('Description', 'cfb-calculator'); ?></label>
                        <textarea id="form-description" placeholder="<?php _e('Enter form description', 'cfb-calculator'); ?>"><?php echo $form ? esc_textarea($form->description) : ''; ?></textarea>
                    </div>
                    
                    <div class="cfb-form-group">
                        <h4><?php _e('Calculation Settings', 'cfb-calculator'); ?></h4>
                        
                        <label>
                            <input type="checkbox" id="enable-subtotals" <?php checked(isset($form_data['enable_subtotals']) && $form_data['enable_subtotals']); ?>>
                            <?php _e('Enable Subtotals', 'cfb-calculator'); ?>
                        </label>
                        
                        <label>
                            <input type="checkbox" id="save-submissions" <?php checked(isset($form_data['save_submissions']) && $form_data['save_submissions']); ?>>
                            <?php _e('Save Submissions', 'cfb-calculator'); ?>
                        </label>
                    </div>
                </div>
                
                <!-- Field Types Palette -->
                <div class="cfb-field-palette">
                    <h3><?php _e('Field Types', 'cfb-calculator'); ?></h3>
                    <div class="cfb-field-types">
                        <?php foreach ($field_types as $type => $config): ?>
                            <div class="cfb-field-type" data-type="<?php echo esc_attr($type); ?>">
                                <span class="dashicons <?php echo esc_attr($config['icon']); ?>"></span>
                                <span class="label"><?php echo esc_html($config['label']); ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Form Builder Canvas -->
                <div class="cfb-builder-canvas">
                    <h3><?php _e('Form Fields', 'cfb-calculator'); ?></h3>
                    <div class="cfb-form-fields" id="cfb-form-fields">
                        <?php if (isset($form_data['fields']) && is_array($form_data['fields'])): ?>
                            <?php foreach ($form_data['fields'] as $field): ?>
                                <?php $this->render_field_editor($field); ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                    
                    <div class="cfb-drop-zone">
                        <p><?php _e('Drag field types here to build your form', 'cfb-calculator'); ?></p>
                    </div>
                </div>
                
                <!-- Subtotals and Total Configuration -->
                <div class="cfb-calculation-panel" id="cfb-calculation-panel" style="display: <?php echo (isset($form_data['enable_subtotals']) && $form_data['enable_subtotals']) ? 'block' : 'none'; ?>;">
                    <h3><?php _e('Calculation Configuration', 'cfb-calculator'); ?></h3>
                    
                    <div class="cfb-subtotals-section">
                        <h4><?php _e('Subtotals', 'cfb-calculator'); ?></h4>
                        <div id="cfb-subtotals-list">
                            <?php if (isset($form_data['subtotals']) && is_array($form_data['subtotals'])): ?>
                                <?php foreach ($form_data['subtotals'] as $index => $subtotal): ?>
                                    <?php $this->render_subtotal_editor($subtotal, $index); ?>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                        <button type="button" class="button" id="add-subtotal"><?php _e('Add Subtotal', 'cfb-calculator'); ?></button>
                    </div>
                    
                    <div class="cfb-total-section">
                        <h4><?php _e('Total Formula', 'cfb-calculator'); ?></h4>
                        <div id="total-formula-builder" data-value="<?php echo esc_attr(isset($form_data['total_formula']) ? $form_data['total_formula'] : ''); ?>"></div>
                        <textarea id="total-formula" style="display: none;" placeholder="<?php _e('Enter total formula (e.g., subtotal_1 + subtotal_2)', 'cfb-calculator'); ?>"><?php echo isset($form_data['total_formula']) ? esc_textarea($form_data['total_formula']) : ''; ?></textarea>
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="cfb-form-actions">
                <button type="button" class="button-primary" id="save-form"><?php _e('Save Form', 'cfb-calculator'); ?></button>
                <button type="button" class="button" id="preview-form"><?php _e('Preview', 'cfb-calculator'); ?></button>
                <a href="<?php echo admin_url('admin.php?page=cfb-calculator'); ?>" class="button"><?php _e('Back to Forms', 'cfb-calculator'); ?></a>
            </div>
            
            <input type="hidden" id="form-id" value="<?php echo $form_id; ?>">

            <!-- Form Data for JavaScript -->
            <script type="text/javascript">
                window.cfbFormData = <?php echo wp_json_encode($form_data); ?>;
                window.cfbFormInfo = <?php echo wp_json_encode($form ? array(
                    'id' => $form->id,
                    'name' => $form->name,
                    'description' => $form->description,
                    'status' => $form->status
                ) : array()); ?>;
            </script>
        </div>
        
        <!-- Field Editor Template -->
        <script type="text/template" id="cfb-field-editor-template">
            <div class="cfb-field-editor" data-field-type="{{type}}">
                <div class="cfb-field-header">
                    <span class="cfb-field-title">{{label}}</span>
                    <div class="cfb-field-actions">
                        <button type="button" class="cfb-edit-field" title="<?php _e('Edit Field', 'cfb-calculator'); ?>">
                            <span class="dashicons dashicons-edit"></span>
                        </button>
                        <button type="button" class="cfb-delete-field" title="<?php _e('Delete Field', 'cfb-calculator'); ?>">
                            <span class="dashicons dashicons-trash"></span>
                        </button>
                    </div>
                </div>
                <div class="cfb-field-preview">
                    <!-- Field preview will be rendered here -->
                </div>
                <div class="cfb-field-settings" style="display: none;">
                    <!-- Field settings will be rendered here -->
                </div>
            </div>
        </script>
        <?php
    }
    
    /**
     * Render field editor for existing field
     */
    private function render_field_editor($field) {
        // This would render the field editor HTML
        // Implementation would be in JavaScript for dynamic editing
    }
    
    /**
     * Render subtotal editor
     */
    private function render_subtotal_editor($subtotal, $index) {
        ?>
        <div class="cfb-subtotal-editor" data-index="<?php echo $index; ?>">
            <div class="cfb-form-group">
                <label><?php _e('Subtotal Label', 'cfb-calculator'); ?></label>
                <input type="text" class="subtotal-label" value="<?php echo esc_attr($subtotal['label']); ?>" placeholder="<?php _e('Enter subtotal label', 'cfb-calculator'); ?>">
            </div>
            <div class="cfb-form-group">
                <label><?php _e('Formula', 'cfb-calculator'); ?></label>
                <textarea class="subtotal-formula" placeholder="<?php _e('Enter formula', 'cfb-calculator'); ?>"><?php echo esc_textarea($subtotal['formula']); ?></textarea>
            </div>
            <button type="button" class="button cfb-remove-subtotal"><?php _e('Remove', 'cfb-calculator'); ?></button>
        </div>
        <?php
    }
}
