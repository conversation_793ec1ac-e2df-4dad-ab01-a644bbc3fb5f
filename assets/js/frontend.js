/**
 * CFB Calculator Frontend JavaScript
 * Handles form interactions, calculations, and conditional logic
 */

(function($) {
    'use strict';

    class CFBCalculator {
        constructor(wrapper) {
            this.wrapper = $(wrapper);
            this.form = this.wrapper.find('.cfb-calculator-form');
            this.formId = this.wrapper.data('form-id');
            this.fields = {};
            this.conditionalFields = [];
            this.autoCalculate = cfb_ajax.auto_calculate || true;
            
            this.init();
        }

        init() {
            this.bindEvents();
            this.initializeFields();
            this.setupConditionalLogic();
            this.setupSliders();
            
            if (this.autoCalculate) {
                this.calculate();
            }
        }

        bindEvents() {
            // Calculate button
            this.wrapper.on('click', '.cfb-calculate-btn', (e) => {
                e.preventDefault();
                this.calculate();
            });

            // Reset button
            this.wrapper.on('click', '.cfb-reset-btn', (e) => {
                e.preventDefault();
                this.resetForm();
            });

            // Auto-calculate on field changes
            if (this.autoCalculate) {
                this.form.on('change input', 'input, select, textarea', () => {
                    this.debounce(() => {
                        this.checkConditionalLogic();
                        this.calculate();
                    }, 300);
                });
            } else {
                this.form.on('change input', 'input, select, textarea', () => {
                    this.checkConditionalLogic();
                });
            }

            // Slider value display
            this.form.on('input', '.cfb-slider-input', (e) => {
                const slider = $(e.target);
                const valueDisplay = slider.siblings('.cfb-slider-value').find('.cfb-slider-current');
                valueDisplay.text(slider.val());
            });
        }

        initializeFields() {
            this.form.find('[name]').each((index, element) => {
                const field = $(element);
                const name = field.attr('name').replace('[]', '');
                
                if (!this.fields[name]) {
                    this.fields[name] = {
                        element: field,
                        type: this.getFieldType(field),
                        wrapper: field.closest('.cfb-field')
                    };
                }
            });
        }

        getFieldType(field) {
            if (field.is('input[type="text"]')) return 'text';
            if (field.is('input[type="number"]')) return 'number';
            if (field.is('input[type="range"]')) return 'slider';
            if (field.is('select')) return 'dropdown';
            if (field.is('input[type="radio"]')) return 'radio';
            if (field.is('input[type="checkbox"]')) return 'checkbox';
            return 'unknown';
        }

        setupConditionalLogic() {
            this.form.find('[data-conditional]').each((index, element) => {
                const field = $(element);
                const conditionalData = field.data('conditional');
                
                if (conditionalData && conditionalData.enabled) {
                    this.conditionalFields.push({
                        field: field,
                        logic: conditionalData
                    });
                }
            });

            this.checkConditionalLogic();
        }

        checkConditionalLogic() {
            this.conditionalFields.forEach(item => {
                const isVisible = this.evaluateConditionalLogic(item.logic);
                
                if (isVisible) {
                    item.field.removeClass('cfb-field-hidden').addClass('cfb-fade-in');
                } else {
                    item.field.addClass('cfb-field-hidden').removeClass('cfb-fade-in');
                }
            });
        }

        evaluateConditionalLogic(logic) {
            let conditionsMet = 0;
            
            logic.conditions.forEach(condition => {
                const fieldValue = this.getFieldValue(condition.field);
                let conditionMet = false;

                switch (condition.operator) {
                    case 'equals':
                        conditionMet = (fieldValue == condition.value);
                        break;
                    case 'not_equals':
                        conditionMet = (fieldValue != condition.value);
                        break;
                    case 'greater_than':
                        conditionMet = (parseFloat(fieldValue) > parseFloat(condition.value));
                        break;
                    case 'less_than':
                        conditionMet = (parseFloat(fieldValue) < parseFloat(condition.value));
                        break;
                    case 'contains':
                        conditionMet = (fieldValue.toString().indexOf(condition.value) !== -1);
                        break;
                    case 'not_empty':
                        conditionMet = (fieldValue !== '' && fieldValue !== null && fieldValue !== undefined);
                        break;
                    case 'empty':
                        conditionMet = (fieldValue === '' || fieldValue === null || fieldValue === undefined);
                        break;
                }

                if (conditionMet) {
                    conditionsMet++;
                }
            });

            if (logic.logic_type === 'all') {
                return conditionsMet === logic.conditions.length;
            } else {
                return conditionsMet > 0;
            }
        }

        getFieldValue(fieldName) {
            const field = this.form.find(`[name="${fieldName}"], [name="${fieldName}[]"]`);
            
            if (field.is('input[type="radio"]')) {
                return field.filter(':checked').val() || '';
            } else if (field.is('input[type="checkbox"]')) {
                const values = [];
                field.filter(':checked').each(function() {
                    values.push($(this).val());
                });
                return values;
            } else {
                return field.val() || '';
            }
        }

        setupSliders() {
            this.form.find('.cfb-slider-input').each(function() {
                const slider = $(this);
                const valueDisplay = slider.siblings('.cfb-slider-value').find('.cfb-slider-current');
                valueDisplay.text(slider.val());
            });
        }

        calculate() {
            const formData = this.getFormData();
            
            this.showLoading();
            this.hideError();

            $.ajax({
                url: cfb_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'cfb_calculate_price',
                    nonce: cfb_ajax.nonce,
                    form_id: this.formId,
                    form_data: formData
                },
                success: (response) => {
                    this.hideLoading();
                    
                    if (response.success) {
                        this.displayResults(response.data);
                        this.animateResults();
                    } else {
                        this.showError(response.data || 'Calculation failed');
                    }
                },
                error: () => {
                    this.hideLoading();
                    this.showError('Network error occurred');
                }
            });
        }

        getFormData() {
            const data = {};
            
            this.form.find('[name]').each(function() {
                const field = $(this);
                const name = field.attr('name').replace('[]', '');
                
                if (field.is('input[type="radio"]')) {
                    if (field.is(':checked')) {
                        data[name] = field.val();
                    }
                } else if (field.is('input[type="checkbox"]')) {
                    if (!data[name]) data[name] = [];
                    if (field.is(':checked')) {
                        data[name].push(field.val());
                    }
                } else {
                    data[name] = field.val();
                }
            });

            return data;
        }

        displayResults(results) {
            // Display subtotals
            if (results.subtotals) {
                results.subtotals.forEach((subtotal, index) => {
                    const subtotalElement = this.wrapper.find(`[data-subtotal="${index}"] .cfb-subtotal-value`);
                    subtotalElement.text(subtotal.formatted);
                });
            }

            // Display total
            const totalElement = this.wrapper.find('.cfb-total-value');
            totalElement.text(results.formatted_total);
        }

        animateResults() {
            const resultsSection = this.wrapper.find('.cfb-calculation-results');
            
            if (resultsSection.length) {
                resultsSection.addClass('cfb-slide-up');
                
                // Animate numbers counting up
                this.animateNumbers();
            }
        }

        animateNumbers() {
            this.wrapper.find('.cfb-subtotal-value, .cfb-total-value').each(function() {
                const element = $(this);
                const text = element.text();
                const number = parseFloat(text.replace(/[^0-9.-]+/g, ''));
                
                if (!isNaN(number)) {
                    element.prop('Counter', 0).animate({
                        Counter: number
                    }, {
                        duration: 1000,
                        easing: 'swing',
                        step: function(now) {
                            const formatted = cfb_calculator.formatCurrency(now);
                            element.text(formatted);
                        },
                        complete: function() {
                            element.text(text); // Ensure final value is exact
                        }
                    });
                }
            });
        }

        formatCurrency(value) {
            const symbol = cfb_ajax.currency_symbol;
            const position = cfb_ajax.currency_position;
            const decimals = parseInt(cfb_ajax.decimal_places);
            const thousandSep = cfb_ajax.thousand_separator;
            const decimalSep = cfb_ajax.decimal_separator;
            
            const formatted = this.numberFormat(value, decimals, decimalSep, thousandSep);
            
            if (position === 'right') {
                return formatted + ' ' + symbol;
            } else {
                return symbol + ' ' + formatted;
            }
        }

        numberFormat(number, decimals, decPoint, thousandsSep) {
            number = (number + '').replace(/[^0-9+\-Ee.]/g, '');
            const n = !isFinite(+number) ? 0 : +number;
            const prec = !isFinite(+decimals) ? 0 : Math.abs(decimals);
            const sep = (typeof thousandsSep === 'undefined') ? ',' : thousandsSep;
            const dec = (typeof decPoint === 'undefined') ? '.' : decPoint;
            
            const s = (prec ? this.toFixedFix(n, prec) : '' + Math.round(n)).split('.');
            
            if (s[0].length > 3) {
                s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
            }
            
            if ((s[1] || '').length < prec) {
                s[1] = s[1] || '';
                s[1] += new Array(prec - s[1].length + 1).join('0');
            }
            
            return s.join(dec);
        }

        toFixedFix(n, prec) {
            const k = Math.pow(10, prec);
            return '' + (Math.round(n * k) / k);
        }

        resetForm() {
            this.form[0].reset();
            
            // Reset sliders
            this.form.find('.cfb-slider-input').each(function() {
                const slider = $(this);
                const defaultValue = slider.attr('value') || slider.attr('min') || 0;
                slider.val(defaultValue);
                slider.siblings('.cfb-slider-value').find('.cfb-slider-current').text(defaultValue);
            });

            // Clear results
            this.wrapper.find('.cfb-subtotal-value, .cfb-total-value').text('-');
            
            // Check conditional logic
            this.checkConditionalLogic();
            
            // Hide error messages
            this.hideError();
        }

        showLoading() {
            this.wrapper.find('.cfb-loading').show();
            this.wrapper.find('.cfb-calculate-btn').prop('disabled', true);
        }

        hideLoading() {
            this.wrapper.find('.cfb-loading').hide();
            this.wrapper.find('.cfb-calculate-btn').prop('disabled', false);
        }

        showError(message) {
            const errorElement = this.wrapper.find('.cfb-error-message');
            errorElement.text(message).show();
        }

        hideError() {
            this.wrapper.find('.cfb-error-message').hide();
        }

        debounce(func, wait) {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = setTimeout(func, wait);
        }
    }

    // Initialize calculators when document is ready
    $(document).ready(function() {
        $('.cfb-calculator-wrapper').each(function() {
            new CFBCalculator(this);
        });
    });

    // Make CFBCalculator globally available
    window.cfb_calculator = {
        formatCurrency: function(value) {
            const instance = new CFBCalculator($('.cfb-calculator-wrapper').first());
            return instance.formatCurrency(value);
        }
    };

})(jQuery);
