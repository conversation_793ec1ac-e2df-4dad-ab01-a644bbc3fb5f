/**
 * CFB Calculator Admin JavaScript
 * Handles form builder, drag & drop, and admin interactions
 */

(function($) {
    'use strict';

    class CFBFormBuilder {
        constructor() {
            this.fieldCounter = 0;
            this.currentForm = {
                id: $('#form-id').val() || 0,
                name: '',
                description: '',
                fields: [],
                subtotals: [],
                settings: {}
            };
            
            this.init();
        }

        init() {
            this.bindEvents();
            this.initializeDragDrop();
            this.initializeTabs();
            this.loadExistingForm();
        }

        bindEvents() {
            // Save form
            $('#save-form').on('click', () => this.saveForm());
            
            // Preview form
            $('#preview-form').on('click', () => this.previewForm());
            
            // Add subtotal
            $('#add-subtotal').on('click', () => this.addSubtotal());
            
            // Remove subtotal
            $(document).on('click', '.cfb-remove-subtotal', (e) => {
                $(e.target).closest('.cfb-subtotal-editor').remove();
            });
            
            // Enable/disable subtotals
            $('#enable-subtotals').on('change', (e) => {
                const panel = $('#cfb-calculation-panel');
                if (e.target.checked) {
                    panel.show();
                } else {
                    panel.hide();
                }
            });
            
            // Field type drag start
            $('.cfb-field-type').on('dragstart', (e) => {
                e.originalEvent.dataTransfer.setData('text/plain', $(e.target).data('type'));
            });
            
            // Form list actions
            $('.cfb-delete-form').on('click', (e) => this.deleteForm(e));
            $('.cfb-duplicate-form').on('click', (e) => this.duplicateForm(e));
            $('.cfb-toggle-status').on('click', (e) => this.toggleFormStatus(e));
            
            // Settings tabs
            $('.cfb-tab').on('click', (e) => this.switchTab(e));
            
            // Settings form
            $('#cfb-settings-form').on('submit', (e) => this.saveSettings(e));
            
            // Reset settings
            $('#cfb-reset-settings').on('click', () => this.resetSettings());
        }

        initializeDragDrop() {
            const canvas = $('#cfb-form-fields');
            const dropZone = $('.cfb-drop-zone');

            // Make field types draggable
            $('.cfb-field-type').attr('draggable', true);

            // Canvas drop events
            canvas.on('dragover', (e) => {
                e.preventDefault();
                dropZone.addClass('drag-over');
            });

            canvas.on('dragleave', (e) => {
                if (!canvas[0].contains(e.relatedTarget)) {
                    dropZone.removeClass('drag-over');
                }
            });

            canvas.on('drop', (e) => {
                e.preventDefault();
                dropZone.removeClass('drag-over');
                
                const fieldType = e.originalEvent.dataTransfer.getData('text/plain');
                if (fieldType) {
                    this.addField(fieldType);
                }
            });

            // Make existing fields sortable
            canvas.sortable({
                handle: '.cfb-field-header',
                placeholder: 'cfb-field-placeholder',
                update: () => this.updateFieldOrder()
            });
        }

        initializeTabs() {
            // Settings page tabs
            $('.cfb-tab').on('click', function() {
                const tabId = $(this).data('tab');
                
                $('.cfb-tab').removeClass('active');
                $('.cfb-tab-content').removeClass('active');
                
                $(this).addClass('active');
                $(`#${tabId}-tab`).addClass('active');
            });
        }

        addField(fieldType) {
            this.fieldCounter++;
            const fieldId = `field_${this.fieldCounter}`;
            
            const fieldConfig = this.getDefaultFieldConfig(fieldType);
            fieldConfig.id = fieldId;
            fieldConfig.name = `${fieldType}_${this.fieldCounter}`;
            
            const fieldHtml = this.renderFieldEditor(fieldConfig);
            
            if ($('#cfb-form-fields .cfb-field-editor').length === 0) {
                $('.cfb-drop-zone').hide();
            }
            
            $('#cfb-form-fields').append(fieldHtml);
            
            // Initialize field settings
            this.initializeFieldSettings(fieldId, fieldConfig);
        }

        getDefaultFieldConfig(type) {
            const defaultConditionalLogic = {
                enabled: false,
                logic_type: 'all',
                conditions: []
            };

            const configs = {
                text: {
                    type: 'text',
                    label: 'Text Field',
                    placeholder: '',
                    required: false,
                    calculation_rule: { type: 'per_character', rate: 0 },
                    conditional_logic: defaultConditionalLogic
                },
                number: {
                    type: 'number',
                    label: 'Number Field',
                    min: 0,
                    max: 100,
                    step: 1,
                    default_value: 0,
                    required: false,
                    conditional_logic: defaultConditionalLogic
                },
                slider: {
                    type: 'slider',
                    label: 'Slider',
                    min: 0,
                    max: 100,
                    step: 1,
                    default_value: 50,
                    show_value: true,
                    conditional_logic: defaultConditionalLogic
                },
                dropdown: {
                    type: 'dropdown',
                    label: 'Dropdown',
                    placeholder: 'Select an option',
                    required: false,
                    options: [
                        { label: 'Option 1', value: 'option1', price: 0 },
                        { label: 'Option 2', value: 'option2', price: 0 }
                    ],
                    conditional_logic: defaultConditionalLogic
                },
                radio: {
                    type: 'radio',
                    label: 'Radio Buttons',
                    required: false,
                    options: [
                        { label: 'Option 1', value: 'option1', price: 0 },
                        { label: 'Option 2', value: 'option2', price: 0 }
                    ],
                    conditional_logic: defaultConditionalLogic
                },
                checkbox: {
                    type: 'checkbox',
                    label: 'Checkboxes',
                    options: [
                        { label: 'Option 1', value: 'option1', price: 0 },
                        { label: 'Option 2', value: 'option2', price: 0 }
                    ],
                    conditional_logic: defaultConditionalLogic
                }
            };

            return configs[type] || {};
        }

        renderFieldEditor(field) {
            return `
                <div class="cfb-field-editor" data-field-id="${field.id}" data-field-type="${field.type}">
                    <div class="cfb-field-header">
                        <span class="cfb-field-title">${field.label}</span>
                        <div class="cfb-field-actions">
                            <button type="button" class="cfb-edit-field" title="Edit Field">
                                <span class="dashicons dashicons-edit"></span>
                            </button>
                            <button type="button" class="cfb-delete-field" title="Delete Field">
                                <span class="dashicons dashicons-trash"></span>
                            </button>
                        </div>
                    </div>
                    <div class="cfb-field-preview">
                        ${this.renderFieldPreview(field)}
                    </div>
                    <div class="cfb-field-settings" style="display: none;">
                        ${this.renderFieldSettings(field)}
                    </div>
                </div>
            `;
        }

        renderFieldPreview(field) {
            switch (field.type) {
                case 'text':
                    return `<input type="text" placeholder="${field.placeholder}" disabled>`;
                case 'number':
                    return `<input type="number" min="${field.min}" max="${field.max}" step="${field.step}" value="${field.default_value}" disabled>`;
                case 'slider':
                    return `<input type="range" min="${field.min}" max="${field.max}" step="${field.step}" value="${field.default_value}" disabled>`;
                case 'dropdown':
                    let options = field.options.map(opt => `<option value="${opt.value}">${opt.label}</option>`).join('');
                    return `<select disabled><option value="">${field.placeholder}</option>${options}</select>`;
                case 'radio':
                    return field.options.map(opt => 
                        `<label><input type="radio" name="preview_${field.id}" value="${opt.value}" disabled> ${opt.label}</label>`
                    ).join('<br>');
                case 'checkbox':
                    return field.options.map(opt => 
                        `<label><input type="checkbox" value="${opt.value}" disabled> ${opt.label}</label>`
                    ).join('<br>');
                default:
                    return '<p>Unknown field type</p>';
            }
        }

        renderFieldSettings(field) {
            let html = `
                <div class="cfb-form-group">
                    <label>Field Label</label>
                    <input type="text" class="field-label" value="${field.label}">
                </div>
                <div class="cfb-form-group">
                    <label>Field Name</label>
                    <input type="text" class="field-name" value="${field.name}">
                </div>
            `;

            // Type-specific settings
            switch (field.type) {
                case 'text':
                    html += `
                        <div class="cfb-form-group">
                            <label>Placeholder</label>
                            <input type="text" class="field-placeholder" value="${field.placeholder}">
                        </div>
                        <div class="cfb-form-group">
                            <label>
                                <input type="checkbox" class="field-required" ${field.required ? 'checked' : ''}>
                                Required
                            </label>
                        </div>
                    `;
                    break;
                case 'number':
                case 'slider':
                    html += `
                        <div class="cfb-form-group">
                            <label>Minimum Value</label>
                            <input type="number" class="field-min" value="${field.min}">
                        </div>
                        <div class="cfb-form-group">
                            <label>Maximum Value</label>
                            <input type="number" class="field-max" value="${field.max}">
                        </div>
                        <div class="cfb-form-group">
                            <label>Step</label>
                            <input type="number" class="field-step" value="${field.step}" step="0.01">
                        </div>
                        <div class="cfb-form-group">
                            <label>Default Value</label>
                            <input type="number" class="field-default" value="${field.default_value}">
                        </div>
                    `;
                    if (field.type === 'slider') {
                        html += `
                            <div class="cfb-form-group">
                                <label>
                                    <input type="checkbox" class="field-show-value" ${field.show_value ? 'checked' : ''}>
                                    Show Value
                                </label>
                            </div>
                        `;
                    }
                    break;
                case 'dropdown':
                case 'radio':
                case 'checkbox':
                    html += `
                        <div class="cfb-form-group">
                            <label>Options</label>
                            <div class="cfb-options-editor">
                                ${this.renderOptionsEditor(field.options)}
                            </div>
                            <button type="button" class="cfb-add-option">Add Option</button>
                        </div>
                    `;
                    break;
            }

            html += `
                <div class="cfb-form-group">
                    <h4>Conditional Logic</h4>
                    <label>
                        <input type="checkbox" class="enable-conditional" ${field.conditional_logic?.enabled ? 'checked' : ''}>
                        Enable conditional logic
                    </label>
                    <div class="cfb-conditional-logic" style="display: ${field.conditional_logic?.enabled ? 'block' : 'none'};">
                        ${this.renderConditionalLogicBuilder(field.conditional_logic || {})}
                    </div>
                </div>
            `;

            return html;
        }

        renderOptionsEditor(options) {
            return options.map((option, index) => `
                <div class="cfb-option-item">
                    <input type="text" placeholder="Label" value="${option.label}" class="option-label">
                    <input type="text" placeholder="Value" value="${option.value}" class="option-value">
                    <input type="number" placeholder="Price" value="${option.price}" class="option-price" step="0.01">
                    <button type="button" class="cfb-remove-option">Remove</button>
                </div>
            `).join('');
        }

        renderConditionalLogicBuilder(conditionalLogic) {
            const conditions = conditionalLogic.conditions || [];
            const logicType = conditionalLogic.logic_type || 'all';

            let html = `
                <div class="cfb-conditional-settings">
                    <div class="cfb-form-group">
                        <label>Show this field if:</label>
                        <select class="conditional-logic-type">
                            <option value="all" ${logicType === 'all' ? 'selected' : ''}>All conditions are met</option>
                            <option value="any" ${logicType === 'any' ? 'selected' : ''}>Any condition is met</option>
                        </select>
                    </div>

                    <div class="cfb-conditions-list">
            `;

            if (conditions.length === 0) {
                html += this.renderConditionRow({});
            } else {
                conditions.forEach((condition, index) => {
                    html += this.renderConditionRow(condition, index);
                });
            }

            html += `
                    </div>

                    <button type="button" class="cfb-add-condition button">Add Condition</button>
                </div>
            `;

            return html;
        }

        renderConditionRow(condition, index = 0) {
            const availableFields = this.getAvailableFieldsForConditions();

            return `
                <div class="cfb-condition-row" data-index="${index}">
                    <div class="cfb-condition-field">
                        <select class="condition-field">
                            <option value="">Select Field</option>
                            ${availableFields.map(field =>
                                `<option value="${field.name}" ${condition.field === field.name ? 'selected' : ''}>${field.label}</option>`
                            ).join('')}
                        </select>
                    </div>

                    <div class="cfb-condition-operator">
                        <select class="condition-operator">
                            <option value="equals" ${condition.operator === 'equals' ? 'selected' : ''}>equals</option>
                            <option value="not_equals" ${condition.operator === 'not_equals' ? 'selected' : ''}>not equals</option>
                            <option value="greater_than" ${condition.operator === 'greater_than' ? 'selected' : ''}>greater than</option>
                            <option value="less_than" ${condition.operator === 'less_than' ? 'selected' : ''}>less than</option>
                            <option value="contains" ${condition.operator === 'contains' ? 'selected' : ''}>contains</option>
                            <option value="not_empty" ${condition.operator === 'not_empty' ? 'selected' : ''}>is not empty</option>
                            <option value="empty" ${condition.operator === 'empty' ? 'selected' : ''}>is empty</option>
                        </select>
                    </div>

                    <div class="cfb-condition-value">
                        <input type="text" class="condition-value" value="${condition.value || ''}" placeholder="Value">
                    </div>

                    <div class="cfb-condition-actions">
                        <button type="button" class="cfb-remove-condition" title="Remove condition">×</button>
                    </div>
                </div>
            `;
        }

        getAvailableFieldsForConditions() {
            const fields = [];
            $('#cfb-form-fields .cfb-field-editor').each((index, element) => {
                const fieldEditor = $(element);
                const fieldType = fieldEditor.data('field-type');
                const fieldName = fieldEditor.find('.field-name').val();
                const fieldLabel = fieldEditor.find('.field-label').val();

                if (fieldName && fieldLabel) {
                    fields.push({
                        name: fieldName,
                        label: fieldLabel,
                        type: fieldType
                    });
                }
            });
            return fields;
        }

        initializeFieldSettings(fieldId, fieldConfig) {
            const fieldEditor = $(`.cfb-field-editor[data-field-id="${fieldId}"]`);
            
            // Edit button
            fieldEditor.find('.cfb-edit-field').on('click', () => {
                const settings = fieldEditor.find('.cfb-field-settings');
                settings.toggle();
            });
            
            // Delete button
            fieldEditor.find('.cfb-delete-field').on('click', () => {
                fieldEditor.remove();
                if ($('#cfb-form-fields .cfb-field-editor').length === 0) {
                    $('.cfb-drop-zone').show();
                }
            });
            
            // Settings change handlers
            fieldEditor.find('.field-label').on('input', (e) => {
                fieldEditor.find('.cfb-field-title').text(e.target.value);
                // Refresh condition field options when field labels change
                this.refreshConditionFieldOptions();
            });

            fieldEditor.find('.field-name').on('input', () => {
                // Refresh condition field options when field names change
                this.refreshConditionFieldOptions();
            });
            
            // Add option button
            fieldEditor.find('.cfb-add-option').on('click', () => {
                const optionsEditor = fieldEditor.find('.cfb-options-editor');
                const newOption = `
                    <div class="cfb-option-item">
                        <input type="text" placeholder="Label" class="option-label">
                        <input type="text" placeholder="Value" class="option-value">
                        <input type="number" placeholder="Price" class="option-price" step="0.01">
                        <button type="button" class="cfb-remove-option">Remove</button>
                    </div>
                `;
                optionsEditor.append(newOption);
            });
            
            // Remove option button
            fieldEditor.on('click', '.cfb-remove-option', (e) => {
                $(e.target).closest('.cfb-option-item').remove();
            });

            // Conditional logic toggle
            fieldEditor.find('.enable-conditional').on('change', (e) => {
                const conditionalPanel = fieldEditor.find('.cfb-conditional-logic');
                if (e.target.checked) {
                    conditionalPanel.show();
                    if (conditionalPanel.find('.cfb-conditions-list').children().length === 0) {
                        conditionalPanel.find('.cfb-conditions-list').html(this.renderConditionRow({}));
                    }
                } else {
                    conditionalPanel.hide();
                }
            });

            // Add condition button
            fieldEditor.on('click', '.cfb-add-condition', () => {
                const conditionsList = fieldEditor.find('.cfb-conditions-list');
                const newIndex = conditionsList.children().length;
                conditionsList.append(this.renderConditionRow({}, newIndex));
            });

            // Remove condition button
            fieldEditor.on('click', '.cfb-remove-condition', (e) => {
                const conditionRow = $(e.target).closest('.cfb-condition-row');
                const conditionsList = conditionRow.parent();

                conditionRow.remove();

                // Ensure at least one condition exists if conditional logic is enabled
                if (conditionsList.children().length === 0 && fieldEditor.find('.enable-conditional').is(':checked')) {
                    conditionsList.append(this.renderConditionRow({}));
                }
            });

            // Update condition field options when field changes
            fieldEditor.on('change', '.condition-field', (e) => {
                const selectedField = $(e.target).val();
                const conditionRow = $(e.target).closest('.cfb-condition-row');
                const valueInput = conditionRow.find('.condition-value');

                // Update value input based on field type
                this.updateConditionValueInput(selectedField, valueInput);
            });

            // Hide value input for certain operators
            fieldEditor.on('change', '.condition-operator', (e) => {
                const operator = $(e.target).val();
                const valueDiv = $(e.target).closest('.cfb-condition-row').find('.cfb-condition-value');

                if (operator === 'not_empty' || operator === 'empty') {
                    valueDiv.hide();
                } else {
                    valueDiv.show();
                }
            });
        }

        updateConditionValueInput(fieldName, valueInput) {
            // Find the field configuration
            const fieldEditor = $(`.cfb-field-editor`).find('.field-name').filter(function() {
                return $(this).val() === fieldName;
            }).closest('.cfb-field-editor');

            if (fieldEditor.length === 0) {
                valueInput.replaceWith('<input type="text" class="condition-value" placeholder="Value">');
                return;
            }

            const fieldType = fieldEditor.data('field-type');

            switch (fieldType) {
                case 'dropdown':
                case 'radio':
                case 'checkbox':
                    // Create select with field options
                    const options = [];
                    fieldEditor.find('.cfb-option-item').each(function() {
                        const label = $(this).find('.option-label').val();
                        const value = $(this).find('.option-value').val();
                        if (label && value) {
                            options.push({ label, value });
                        }
                    });

                    let selectHtml = '<select class="condition-value"><option value="">Select value</option>';
                    options.forEach(option => {
                        selectHtml += `<option value="${option.value}">${option.label}</option>`;
                    });
                    selectHtml += '</select>';

                    valueInput.replaceWith(selectHtml);
                    break;

                case 'number':
                case 'slider':
                    valueInput.replaceWith('<input type="number" class="condition-value" placeholder="Number">');
                    break;

                default:
                    valueInput.replaceWith('<input type="text" class="condition-value" placeholder="Value">');
                    break;
            }
        }

        addSubtotal() {
            const index = $('#cfb-subtotals-list .cfb-subtotal-editor').length;
            const subtotalHtml = `
                <div class="cfb-subtotal-editor" data-index="${index}">
                    <div class="cfb-form-group">
                        <label>Subtotal Label</label>
                        <input type="text" class="subtotal-label" placeholder="Enter subtotal label">
                    </div>
                    <div class="cfb-form-group">
                        <label>Formula</label>
                        <textarea class="subtotal-formula" placeholder="Enter formula"></textarea>
                    </div>
                    <button type="button" class="cfb-remove-subtotal">Remove</button>
                </div>
            `;
            
            $('#cfb-subtotals-list').append(subtotalHtml);
        }

        updateFieldOrder() {
            // Update field order when fields are reordered
            $('#cfb-form-fields .cfb-field-editor').each((index, element) => {
                $(element).attr('data-order', index);
            });
        }

        saveForm() {
            const formData = this.collectFormData();
            
            $.ajax({
                url: cfb_admin_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'cfb_save_form',
                    nonce: cfb_admin_ajax.nonce,
                    form_id: this.currentForm.id,
                    form_data: formData
                },
                success: (response) => {
                    if (response.success) {
                        this.showMessage('Form saved successfully!', 'success');
                        if (!this.currentForm.id) {
                            this.currentForm.id = response.data.form_id;
                            $('#form-id').val(this.currentForm.id);
                        }
                    } else {
                        this.showMessage(response.data || 'Failed to save form', 'error');
                    }
                },
                error: () => {
                    this.showMessage('Network error occurred', 'error');
                }
            });
        }

        collectFormData() {
            const data = {
                name: $('#form-name').val(),
                description: $('#form-description').val(),
                enable_subtotals: $('#enable-subtotals').is(':checked'),
                save_submissions: $('#save-submissions').is(':checked'),
                fields: [],
                subtotals: [],
                total_formula: $('#total-formula').val(),
                settings: {}
            };

            // Collect fields
            $('#cfb-form-fields .cfb-field-editor').each((index, element) => {
                const fieldEditor = $(element);
                const fieldData = this.collectFieldData(fieldEditor);
                if (fieldData) {
                    data.fields.push(fieldData);
                }
            });

            // Collect subtotals
            $('#cfb-subtotals-list .cfb-subtotal-editor').each((index, element) => {
                const subtotalEditor = $(element);
                const subtotalData = {
                    label: subtotalEditor.find('.subtotal-label').val(),
                    formula: subtotalEditor.find('.subtotal-formula').val()
                };
                if (subtotalData.label && subtotalData.formula) {
                    data.subtotals.push(subtotalData);
                }
            });

            return data;
        }

        collectFieldData(fieldEditor) {
            const fieldType = fieldEditor.data('field-type');
            const settings = fieldEditor.find('.cfb-field-settings');

            const data = {
                type: fieldType,
                label: settings.find('.field-label').val(),
                name: settings.find('.field-name').val(),
                required: settings.find('.field-required').is(':checked')
            };

            // Type-specific data collection
            switch (fieldType) {
                case 'text':
                    data.placeholder = settings.find('.field-placeholder').val();
                    break;
                case 'number':
                case 'slider':
                    data.min = parseFloat(settings.find('.field-min').val()) || 0;
                    data.max = parseFloat(settings.find('.field-max').val()) || 100;
                    data.step = parseFloat(settings.find('.field-step').val()) || 1;
                    data.default_value = parseFloat(settings.find('.field-default').val()) || 0;
                    if (fieldType === 'slider') {
                        data.show_value = settings.find('.field-show-value').is(':checked');
                    }
                    break;
                case 'dropdown':
                case 'radio':
                case 'checkbox':
                    data.options = [];
                    settings.find('.cfb-option-item').each((index, element) => {
                        const option = $(element);
                        const optionData = {
                            label: option.find('.option-label').val(),
                            value: option.find('.option-value').val(),
                            price: parseFloat(option.find('.option-price').val()) || 0
                        };
                        if (optionData.label && optionData.value) {
                            data.options.push(optionData);
                        }
                    });
                    break;
            }

            // Collect conditional logic data
            const conditionalEnabled = settings.find('.enable-conditional').is(':checked');
            data.conditional_logic = {
                enabled: conditionalEnabled
            };

            if (conditionalEnabled) {
                data.conditional_logic.logic_type = settings.find('.conditional-logic-type').val() || 'all';
                data.conditional_logic.conditions = [];

                settings.find('.cfb-condition-row').each((index, element) => {
                    const conditionRow = $(element);
                    const condition = {
                        field: conditionRow.find('.condition-field').val(),
                        operator: conditionRow.find('.condition-operator').val(),
                        value: conditionRow.find('.condition-value').val()
                    };

                    // Only add condition if field and operator are selected
                    if (condition.field && condition.operator) {
                        // For operators that don't need a value, set empty string
                        if (condition.operator === 'not_empty' || condition.operator === 'empty') {
                            condition.value = '';
                        }
                        data.conditional_logic.conditions.push(condition);
                    }
                });
            }

            return data;
        }

        // Helper method to refresh condition field options
        refreshConditionFieldOptions() {
            $('.cfb-conditional-logic .condition-field').each((index, element) => {
                const currentValue = $(element).val();
                const availableFields = this.getAvailableFieldsForConditions();

                let optionsHtml = '<option value="">Select Field</option>';
                availableFields.forEach(field => {
                    const selected = field.name === currentValue ? 'selected' : '';
                    optionsHtml += `<option value="${field.name}" ${selected}>${field.label}</option>`;
                });

                $(element).html(optionsHtml);
            });
        }

        showMessage(message, type) {
            const messageHtml = `<div class="cfb-message ${type}">${message}</div>`;
            $('.wrap').prepend(messageHtml);
            
            setTimeout(() => {
                $('.cfb-message').fadeOut(() => {
                    $('.cfb-message').remove();
                });
            }, 3000);
        }
    }

    // Initialize when document is ready
    $(document).ready(() => {
        if ($('.cfb-form-builder').length || $('.cfb-settings-page').length) {
            new CFBFormBuilder();
        }
        
        // Shortcode copy functionality
        $('.cfb-shortcode').on('click', function() {
            const text = $(this).text();
            navigator.clipboard.writeText(text).then(() => {
                $(this).css('background', '#d4edda').delay(1000).queue(function() {
                    $(this).css('background', '#f6f7f7').dequeue();
                });
            });
        });
    });

})(jQuery);
