/* CFB Calculator Admin Styles */

/* Form Builder Layout */
.cfb-form-builder {
    background: #f1f1f1;
    margin: 20px 0;
}

.cfb-builder-container {
    display: grid;
    grid-template-columns: 250px 200px 1fr 300px;
    gap: 20px;
    margin-top: 20px;
    min-height: 600px;
}

/* Settings Panel */
.cfb-settings-panel {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    height: fit-content;
}

.cfb-settings-panel h3 {
    margin-top: 0;
    color: #23282d;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 10px;
}

.cfb-form-group {
    margin-bottom: 20px;
}

.cfb-form-group label {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
    color: #23282d;
}

.cfb-form-group input[type="text"],
.cfb-form-group textarea,
.cfb-form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.cfb-form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.cfb-form-group input[type="checkbox"] {
    margin-right: 8px;
}

/* Field Palette */
.cfb-field-palette {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    height: fit-content;
}

.cfb-field-palette h3 {
    margin-top: 0;
    color: #23282d;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 10px;
}

.cfb-field-types {
    display: grid;
    gap: 10px;
}

.cfb-field-type {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: grab;
    transition: all 0.2s ease;
}

.cfb-field-type:hover {
    background: #e8f4fd;
    border-color: #0073aa;
}

.cfb-field-type:active {
    cursor: grabbing;
}

.cfb-field-type .dashicons {
    color: #0073aa;
    font-size: 18px;
}

.cfb-field-type .label {
    font-size: 13px;
    font-weight: 500;
}

/* Builder Canvas */
.cfb-builder-canvas {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    min-height: 500px;
}

.cfb-builder-canvas h3 {
    margin-top: 0;
    color: #23282d;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 10px;
}

.cfb-form-fields {
    min-height: 300px;
    border: 2px dashed #ddd;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.cfb-drop-zone {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-style: italic;
}

.cfb-drop-zone.drag-over {
    border-color: #0073aa;
    background: #e8f4fd;
}

/* Field Editor */
.cfb-field-editor {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 15px;
    transition: all 0.2s ease;
}

.cfb-field-editor:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.cfb-field-editor.active {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

.cfb-field-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: #fff;
    border-bottom: 1px solid #ddd;
    cursor: move;
}

.cfb-field-title {
    font-weight: 600;
    color: #23282d;
}

.cfb-field-actions {
    display: flex;
    gap: 5px;
}

.cfb-field-actions button {
    background: none;
    border: none;
    padding: 4px;
    cursor: pointer;
    border-radius: 2px;
    color: #666;
}

.cfb-field-actions button:hover {
    background: #f0f0f0;
    color: #0073aa;
}

.cfb-field-preview {
    padding: 15px;
}

.cfb-field-settings {
    padding: 15px;
    background: #f6f7f7;
    border-top: 1px solid #ddd;
}

/* Calculation Panel */
.cfb-calculation-panel {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    height: fit-content;
}

.cfb-calculation-panel h3 {
    margin-top: 0;
    color: #23282d;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 10px;
}

.cfb-subtotals-section h4,
.cfb-total-section h4 {
    color: #23282d;
    margin-bottom: 15px;
}

.cfb-subtotal-editor {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

.cfb-subtotal-editor .cfb-form-group {
    margin-bottom: 15px;
}

.cfb-subtotal-editor .cfb-form-group:last-child {
    margin-bottom: 0;
}

.cfb-remove-subtotal {
    background: #dc3545;
    color: #fff;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.cfb-remove-subtotal:hover {
    background: #c82333;
}

#add-subtotal {
    background: #28a745;
    color: #fff;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

#add-subtotal:hover {
    background: #218838;
}

#total-formula {
    width: 100%;
    min-height: 80px;
    font-family: monospace;
    font-size: 13px;
}

/* Form Actions */
.cfb-form-actions {
    margin-top: 30px;
    padding: 20px;
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    text-align: center;
}

.cfb-form-actions .button {
    margin: 0 5px;
}

/* Field Settings Modal */
.cfb-field-settings-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 100000;
    display: none;
}

.cfb-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    border-radius: 4px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.cfb-modal-header {
    padding: 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cfb-modal-title {
    margin: 0;
    font-size: 18px;
    color: #23282d;
}

.cfb-modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
}

.cfb-modal-body {
    padding: 20px;
}

.cfb-modal-footer {
    padding: 20px;
    border-top: 1px solid #ddd;
    text-align: right;
}

/* Conditional Logic Builder */
.cfb-conditional-logic {
    background: #f6f7f7;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-top: 15px;
}

.cfb-conditional-settings {
    margin-bottom: 15px;
}

.cfb-conditional-settings .cfb-form-group {
    margin-bottom: 15px;
}

.cfb-conditional-settings label {
    font-weight: 600;
    margin-bottom: 5px;
    display: block;
}

.conditional-logic-type {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.cfb-conditions-list {
    margin-bottom: 15px;
}

.cfb-condition-row {
    display: grid;
    grid-template-columns: 1fr 120px 1fr 30px;
    gap: 10px;
    align-items: center;
    margin-bottom: 10px;
    padding: 12px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.cfb-condition-row:hover {
    border-color: #0073aa;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.cfb-condition-field select,
.cfb-condition-operator select,
.cfb-condition-value input,
.cfb-condition-value select {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
}

.cfb-condition-actions {
    text-align: center;
}

.cfb-remove-condition {
    background: #dc3545;
    color: #fff;
    border: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.cfb-remove-condition:hover {
    background: #c82333;
    transform: scale(1.1);
}

.cfb-add-condition {
    background: #0073aa;
    color: #fff;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s ease;
}

.cfb-add-condition:hover {
    background: #005a87;
}

/* Conditional Logic States */
.cfb-condition-value[style*="display: none"] {
    display: none !important;
}

/* Conditional Logic Responsive */
@media (max-width: 768px) {
    .cfb-condition-row {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .cfb-condition-actions {
        text-align: right;
    }
}

/* Options Editor */
.cfb-options-editor {
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.cfb-option-item {
    display: grid;
    grid-template-columns: 1fr 1fr auto auto;
    gap: 10px;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #ddd;
    background: #fff;
}

.cfb-option-item:last-child {
    border-bottom: none;
}

.cfb-option-item input {
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px;
}

.cfb-add-option {
    background: #28a745;
    color: #fff;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
}

.cfb-remove-option {
    background: #dc3545;
    color: #fff;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
}

/* Responsive Design */
@media (max-width: 1400px) {
    .cfb-builder-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .cfb-settings-panel,
    .cfb-field-palette,
    .cfb-calculation-panel {
        order: 1;
    }
    
    .cfb-builder-canvas {
        order: 2;
    }
}

@media (max-width: 768px) {
    .cfb-builder-container {
        margin: 10px;
    }
    
    .cfb-field-types {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .cfb-condition {
        grid-template-columns: 1fr;
        gap: 5px;
    }
    
    .cfb-option-item {
        grid-template-columns: 1fr;
        gap: 5px;
    }
}

/* Loading States */
.cfb-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.cfb-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.cfb-message {
    padding: 12px 16px;
    border-radius: 4px;
    margin: 15px 0;
    font-size: 14px;
}

.cfb-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.cfb-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.cfb-message.warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Drag and Drop States */
.cfb-field-editor.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.cfb-drop-zone.drag-over {
    background: #e8f4fd;
    border-color: #0073aa;
}

.cfb-field-editor.drop-target {
    border-top: 3px solid #0073aa;
}
